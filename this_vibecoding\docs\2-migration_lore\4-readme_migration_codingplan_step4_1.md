# LORE-TSR 迁移项目 - 迭代4步骤4.1 渐进式小步迁移计划

## 📋 项目概述

### 当前迭代状态
- **当前迭代**: 迭代4 - 损失函数完整迁移
- **当前步骤**: 步骤4.1 - 核心损失函数实现
- **依赖迭代**: 迭代1,2,3 (已完成)
- **预估时间**: 1天

### 迁移目标
将LORE-TSR的基础损失函数（3个组件）扩展为完整损失函数（6个组件），严格遵循"复制保留核心算法"原则，逐行复制原LORE-TSR的损失函数逻辑，确保数值精度完全一致。

## 🗺️ 文件迁移映射表 (File Migration Map)

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `已完成` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `已完成` |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | `已完成` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | **复杂** | `进行中` |
| `src/lib/models/utils.py` | `networks/lore_tsr/loss_utils.py` | 复制保留：辅助函数模块 | 迭代4 | 简单 | `进行中` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | `已完成` |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5 | **复杂** | `部分完成` |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留：后处理工具 | 迭代11 | 简单 | `未开始` |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离：可变形卷积 | 迭代7 | 简单 | `未开始` |
| `src/lib/models/networks/NMS/` | `external/lore_tsr/NMS/` | 复制隔离：非极大值抑制 | 迭代7 | 简单 | `未开始` |

## 🔄 当前迭代逻辑图 (Current Iteration Logic Diagram)

```mermaid
graph TD
    %% 当前迭代：迭代4步骤4.1 - 核心损失函数实现

    subgraph "Source: LORE-TSR/src/lib/models/losses.py"
        direction LR
        src_focal["FocalLoss (_neg_loss)"]
        src_regl1["RegL1Loss"]
        src_pair["PairLoss (复杂配对逻辑)"]
        src_axis["AxisLoss (轴向损失)"]
    end

    subgraph "Source: LORE-TSR/src/lib/models/utils.py"
        direction LR
        src_gather["_gather_feat"]
        src_transpose["_tranpose_and_gather_feat"]
    end

    subgraph "Target: train-anything/networks/lore_tsr/"
        direction TB
        T1["lore_tsr_loss.py (扩展)"]
        T2["loss_utils.py (新增)"]
        T3["LoreTsrLoss (完整版)"]
        T4["PairLoss (新增)"]
        T5["AxisLoss (新增)"]
    end

    subgraph "Target: modules/utils/lore_tsr/"
        T6["dummy_processor.py (占位)"]
    end

    %% 迁移映射 - 复制保留策略
    src_focal -- "Copy & Preserve" --> T1
    src_regl1 -- "Copy & Preserve" --> T1
    src_pair -- "Copy & Preserve" --> T4
    src_axis -- "Copy & Preserve" --> T5
    
    src_gather -- "Copy & Preserve" --> T2
    src_transpose -- "Copy & Preserve" --> T2

    %% 组合关系
    T1 --> T3
    T4 --> T3
    T5 --> T3
    T2 -.-> T3
    T6 -.-> T3

    %% 依赖关系
    T2 -.-> T4
    T2 -.-> T5
```

## 🎯 目标目录结构树 (Target Directory Tree)

```text
train-anything/
├── networks/lore_tsr/
│   ├── lore_tsr_loss.py                          # [修改] 扩展为完整损失函数
│   ├── loss_utils.py                             # [新增] 损失函数辅助工具
│   └── ...
├── modules/utils/lore_tsr/
│   ├── dummy_processor.py                        # [新增] 占位Processor实现
│   └── ...
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                      # [修改] 扩展损失配置
└── test_lore_tsr_step4_1.py                      # [新增] 迭代4验证测试
```

## 📝 渐进式小步迁移计划

### 步骤4.1.1: 创建损失函数辅助工具模块
**目标**: 创建loss_utils.py，复制LORE-TSR的核心辅助函数
**影响文件**: 
- 新增 `networks/lore_tsr/loss_utils.py`

**具体操作**:
1. 从LORE-TSR的utils.py复制_gather_feat和_tranpose_and_gather_feat函数
2. 保持原有算法逻辑完全不变
3. 调整import路径适配train-anything框架

**代码模板**:
```python
#!/usr/bin/env python3
"""
LORE-TSR 损失函数辅助工具模块
从原LORE-TSR的utils.py复制核心辅助函数，保持算法逻辑不变
"""

import torch

def _gather_feat(feat, ind, mask=None):
    """特征收集函数 - 从LORE-TSR逐行复制"""
    dim = feat.size(2)
    ind = ind.unsqueeze(2).expand(ind.size(0), ind.size(1), dim)
    feat = feat.gather(1, ind)
    if mask is not None:
        mask = mask.unsqueeze(2).expand_as(feat)
        feat = feat[mask]
        feat = feat.view(-1, dim)
    return feat

def _tranpose_and_gather_feat(feat, ind):
    """转置并收集特征 - LORE-TSR核心函数"""
    feat = feat.permute(0, 2, 3, 1).contiguous()
    feat = feat.view(feat.size(0), -1, feat.size(3))
    feat = _gather_feat(feat, ind)
    return feat
```

**验证命令**:
```bash
python -c "
import sys
sys.path.append('train-anything')
from networks.lore_tsr.loss_utils import _gather_feat, _tranpose_and_gather_feat
import torch
print('✅ 辅助函数导入成功')

# 测试_gather_feat
feat = torch.randn(2, 100, 8)
ind = torch.randint(0, 100, (2, 50))
result = _gather_feat(feat, ind)
print(f'✅ _gather_feat测试成功，输出形状: {result.shape}')

# 测试_tranpose_and_gather_feat  
feat = torch.randn(2, 8, 192, 192)
ind = torch.randint(0, 192*192, (2, 50))
result = _tranpose_and_gather_feat(feat, ind)
print(f'✅ _tranpose_and_gather_feat测试成功，输出形状: {result.shape}')
"
```

### 步骤4.1.2: 扩展损失函数类实现
**目标**: 在lore_tsr_loss.py中添加PairLoss和AxisLoss类
**影响文件**: 
- 修改 `networks/lore_tsr/lore_tsr_loss.py`

**具体操作**:
1. 从LORE-TSR的losses.py逐行复制PairLoss类
2. 从LORE-TSR的losses.py逐行复制AxisLoss类  
3. 导入loss_utils中的辅助函数
4. 保持原有计算逻辑完全不变

**验证命令**:
```bash
python -c "
import sys
sys.path.append('train-anything')
from networks.lore_tsr.lore_tsr_loss import PairLoss, AxisLoss
import torch
print('✅ 新增损失函数类导入成功')

# 测试PairLoss
pair_loss = PairLoss()
print('✅ PairLoss实例化成功')

# 测试AxisLoss
axis_loss = AxisLoss()
print('✅ AxisLoss实例化成功')
"
```

### 步骤4.1.4: 创建DummyProcessor占位实现
**目标**: 创建占位Processor实现，为迭代6预留接口
**影响文件**:
- 新增 `modules/utils/lore_tsr/dummy_processor.py`

**具体操作**:
1. 创建DummyProcessor类，提供get_logic_axis方法
2. 返回占位的逻辑轴向信息，供AxisLoss使用
3. 为迭代6的完整Processor实现预留清晰接口

**代码模板**:
```python
#!/usr/bin/env python3
"""
LORE-TSR 占位Processor实现
迭代4：提供基础接口，返回占位数据
迭代6：将实现完整的Processor和Transformer功能
"""

import torch
import torch.nn as nn

class DummyProcessor(nn.Module):
    """占位Processor实现，为迭代6预留接口"""

    def __init__(self, config):
        super().__init__()
        self.config = config

    def get_logic_axis(self, outputs, batch):
        """
        返回占位的逻辑轴向信息

        Args:
            outputs: 模型输出
            batch: 批次数据

        Returns:
            占位的逻辑轴向张量
        """
        batch_size = batch['input'].shape[0] if 'input' in batch else 2
        max_objs = self.config.model.get('max_objs', 500)

        # 返回占位的逻辑轴向信息
        dummy_logic = torch.zeros(batch_size, max_objs, 4)
        if torch.cuda.is_available():
            dummy_logic = dummy_logic.cuda()

        return dummy_logic

    def get_stacked_logic_axis(self, outputs, batch):
        """
        返回占位的堆叠逻辑轴向信息

        Args:
            outputs: 模型输出
            batch: 批次数据

        Returns:
            占位的堆叠逻辑轴向张量
        """
        return self.get_logic_axis(outputs, batch)
```

**验证命令**:
```bash
python -c "
import sys
sys.path.append('train-anything')
from modules.utils.lore_tsr.dummy_processor import DummyProcessor
from omegaconf import DictConfig
import torch

config = DictConfig({
    'model': {
        'max_objs': 500
    }
})

processor = DummyProcessor(config)
print('✅ DummyProcessor创建成功')

# 测试占位方法
batch = {'input': torch.randn(2, 3, 768, 768)}
outputs = {}

logic_axis = processor.get_logic_axis(outputs, batch)
print(f'✅ 占位逻辑轴向生成成功，形状: {logic_axis.shape}')

stacked_logic = processor.get_stacked_logic_axis(outputs, batch)
print(f'✅ 占位堆叠逻辑轴向生成成功，形状: {stacked_logic.shape}')
"
```

### 步骤4.1.5: 创建验证测试脚本
**目标**: 创建完整的验证测试，确保所有损失组件正常工作
**影响文件**:
- 新增 `test_lore_tsr_step4_1.py`

**具体操作**:
1. 创建综合测试脚本，验证所有新增功能
2. 测试完整损失函数的前向传播
3. 验证条件损失开关功能
4. 确保与原LORE-TSR数值一致性

**验证命令**:
```bash
python test_lore_tsr_step4_1.py
```

**预期输出**:
```text
============================================================
LORE-TSR 步骤4.1 验证测试
============================================================
✅ 辅助函数模块测试成功
✅ 新增损失函数类测试成功
✅ 完整损失函数测试成功
✅ DummyProcessor测试成功
✅ 条件损失开关测试成功
============================================================
所有测试通过！步骤4.1验证成功
============================================================
```

## ⚠️ 风险点与缓解措施

### 技术风险
1. **复杂损失函数实现错误**
   - 缓解措施: 逐行对比原代码，严格单元测试
   - 应急方案: 回退到基础版本损失函数

2. **mask处理逻辑不一致**
   - 缓解措施: 详细的数值验证测试
   - 应急方案: 使用简化的mask处理逻辑

3. **占位实现影响训练效果**
   - 缓解措施: 确保占位实现不影响基础损失计算
   - 应急方案: 临时禁用依赖占位的损失组件

### 集成风险
1. **与现有训练循环不兼容**
   - 缓解措施: 保持接口一致性，渐进式集成
   - 应急方案: 通过配置开关选择损失函数版本

## 📈 成功标准

### 功能验收
- ✅ 所有6个损失组件正常工作
- ✅ 损失权重配置正确生效
- ✅ 条件损失开关功能正常
- ✅ 辅助函数模块正常工作
- ✅ DummyProcessor占位实现正常

### 性能验收
- ✅ 训练循环正常运行
- ✅ 损失计算性能满足要求
- ✅ 内存使用无异常增长

### 兼容性验收
- ✅ 向后兼容现有配置
- ✅ 不影响其他训练循环
- ✅ 为后续迭代预留扩展点

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**迭代范围**: 迭代4步骤4.1 - 核心损失函数实现
**预估工期**: 1个工作日
**依赖迭代**: 迭代1,2,3 (已完成)
**后续步骤**: 步骤4.2 - 配置系统扩展
